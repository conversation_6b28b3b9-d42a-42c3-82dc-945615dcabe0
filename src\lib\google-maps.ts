// Google Maps configuration and utilities
declare const google: any // Declare the google variable to fix lint error
export const GOOGLE_MAPS_API_KEY = import.meta.env.VITE_GOOGLE_MAPS_API_KEY || "YOUR_API_KEY_HERE"

export interface MapLocation {
  lat: number
  lng: number
}

export interface PharmacyLocation extends MapLocation {
  id: string
  name: string
  address: string
  phone: string
  rating: number
  isOpen: boolean
}

// Load Google Maps API
export const loadGoogleMapsAPI = (): Promise<typeof google> => {
  return new Promise((resolve, reject) => {
    if (typeof google !== "undefined" && google.maps) {
      resolve(google)
      return
    }

    const script = document.createElement("script")
    script.src = `https://maps.googleapis.com/maps/api/js?key=${GOOGLE_MAPS_API_KEY}&libraries=places`
    script.async = true
    script.defer = true

    script.onload = () => {
      if (typeof google !== "undefined" && google.maps) {
        resolve(google)
      } else {
        reject(new Error("Google Maps API failed to load"))
      }
    }

    script.onerror = () => {
      reject(new Error("Failed to load Google Maps API"))
    }

    document.head.appendChild(script)
  })
}

// Calculate distance between two points
export const calculateDistance = (point1: MapLocation, point2: MapLocation): number => {
  const R = 6371 // Earth's radius in kilometers
  const dLat = (point2.lat - point1.lat) * (Math.PI / 180)
  const dLng = (point2.lng - point1.lng) * (Math.PI / 180)
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(point1.lat * (Math.PI / 180)) *
      Math.cos(point2.lat * (Math.PI / 180)) *
      Math.sin(dLng / 2) *
      Math.sin(dLng / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return R * c
}

// Get user's current location
export const getCurrentLocation = (): Promise<MapLocation> => {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error("Geolocation is not supported"))
      return
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        resolve({
          lat: position.coords.latitude,
          lng: position.coords.longitude,
        })
      },
      (error) => {
        reject(error)
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000,
      },
    )
  })
}
