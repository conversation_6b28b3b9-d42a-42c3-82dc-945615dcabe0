"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import { UserMenu } from "@/components/user-menu"
import { CartButton } from "@/components/cart-button"
import { Package, Truck, CheckCircle, Clock, MapPin, Phone, ArrowLeft, Navigation, Star, RotateCcw } from "lucide-react"
import Link from "next/link"
import { usePara<PERSON>, useRouter } from "next/navigation"

interface OrderDetails {
  id: string
  orderNumber: string
  date: string
  status: "pending" | "confirmed" | "shipped" | "delivered" | "cancelled"
  total: number
  pharmacy: {
    name: string
    address: string
    phone: string
    rating: number
  }
  items: {
    id: string
    name: string
    quantity: number
    price: number
    image?: string
  }[]
  deliveryMethod: "delivery" | "pickup"
  deliveryAddress?: string
  estimatedDelivery?: string
  trackingNumber?: string
  timeline: {
    status: string
    timestamp: string
    description: string
    completed: boolean
  }[]
  deliveryPerson?: {
    name: string
    phone: string
    vehicle: string
  }
}

export default function OrderTrackingPage() {
  const params = useParams()
  const router = useRouter()
  const [order, setOrder] = useState<OrderDetails | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simulate API call to fetch order details
    const fetchOrder = async () => {
      setIsLoading(true)

      // Mock order data
      const mockOrder: OrderDetails = {
        id: params.id as string,
        orderNumber: `ORD-2024-${params.id}`,
        date: "2024-01-28",
        status: "shipped",
        total: 18500,
        pharmacy: {
          name: "Pharmacie du Centre",
          address: "Avenue Kennedy, Akwa, Douala",
          phone: "+237 233 42 15 67",
          rating: 4.8,
        },
        items: [
          {
            id: "1",
            name: "Paracetamol 500mg",
            quantity: 2,
            price: 2500,
          },
          {
            id: "2",
            name: "Coartem (Artemether/Lumefantrine)",
            quantity: 1,
            price: 8500,
          },
          {
            id: "3",
            name: "Vitamin D3 1000IU",
            quantity: 1,
            price: 5000,
          },
        ],
        deliveryMethod: "delivery",
        deliveryAddress: "123 Main Street, Bonanjo, Douala",
        estimatedDelivery: "2024-01-29",
        trackingNumber: "TRK123456789",
        timeline: [
          {
            status: "Order Placed",
            timestamp: "2024-01-28 10:30 AM",
            description: "Your order has been successfully placed",
            completed: true,
          },
          {
            status: "Order Confirmed",
            timestamp: "2024-01-28 11:15 AM",
            description: "Pharmacy confirmed your order and started preparation",
            completed: true,
          },
          {
            status: "Out for Delivery",
            timestamp: "2024-01-28 2:45 PM",
            description: "Your order is on the way to your delivery address",
            completed: true,
          },
          {
            status: "Delivered",
            timestamp: "Expected: 2024-01-29 4:00 PM",
            description: "Your order will be delivered to your address",
            completed: false,
          },
        ],
        deliveryPerson: {
          name: "Jean-Paul Mbarga",
          phone: "+237 677 12 34 56",
          vehicle: "Motorcycle - CM 1234 AB",
        },
      }

      // Simulate network delay
      await new Promise((resolve) => setTimeout(resolve, 1000))

      setOrder(mockOrder)
      setIsLoading(false)
    }

    fetchOrder()
  }, [params.id])

  const getStatusProgress = (status: string) => {
    switch (status) {
      case "pending":
        return 25
      case "confirmed":
        return 50
      case "shipped":
        return 75
      case "delivered":
        return 100
      case "cancelled":
        return 0
      default:
        return 0
    }
  }

  const handleCallPharmacy = () => {
    if (order) {
      window.open(`tel:${order.pharmacy.phone}`, "_self")
    }
  }

  const handleCallDelivery = () => {
    if (order?.deliveryPerson) {
      window.open(`tel:${order.deliveryPerson.phone}`, "_self")
    }
  }

  const handleGetDirections = () => {
    if (order) {
      const destination = encodeURIComponent(order.pharmacy.address)
      const url = `https://www.google.com/maps/dir/?api=1&destination=${destination}`
      window.open(url, "_blank")
    }
  }

  const handleReorder = () => {
    // In a real app, this would add items back to cart
    alert("Items added to cart!")
    router.push("/cart")
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Package className="h-16 w-16 text-gray-400 mx-auto mb-4 animate-pulse" />
          <p className="text-gray-600">Loading order details...</p>
        </div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-600 mb-2">Order not found</h2>
          <p className="text-gray-500 mb-4">The order you're looking for doesn't exist</p>
          <Button asChild>
            <Link href="/orders">Back to Orders</Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="border-b bg-white sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="text-2xl font-bold text-blue-600">
            PharmaCare
          </Link>
          <div className="flex items-center space-x-4">
            <Button variant="outline" asChild>
              <Link href="/orders">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Orders
              </Link>
            </Button>
            <CartButton />
            <UserMenu />
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Order Tracking</h1>
          <p className="text-gray-600">Track your order #{order.orderNumber}</p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Order Timeline */}
          <div className="lg:col-span-2 space-y-6">
            {/* Status Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Order Status</span>
                  <Badge
                    className={`${
                      order.status === "delivered"
                        ? "bg-green-100 text-green-800"
                        : order.status === "shipped"
                          ? "bg-blue-100 text-blue-800"
                          : order.status === "confirmed"
                            ? "bg-yellow-100 text-yellow-800"
                            : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Progress</span>
                      <span>{getStatusProgress(order.status)}%</span>
                    </div>
                    <Progress value={getStatusProgress(order.status)} className="h-2" />
                  </div>
                  {order.estimatedDelivery && (
                    <div className="flex items-center text-sm text-gray-600">
                      <Clock className="h-4 w-4 mr-2" />
                      <span>Estimated delivery: {new Date(order.estimatedDelivery).toLocaleDateString()}</span>
                    </div>
                  )}
                  {order.trackingNumber && (
                    <div className="flex items-center text-sm text-gray-600">
                      <Package className="h-4 w-4 mr-2" />
                      <span>Tracking number: {order.trackingNumber}</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Timeline */}
            <Card>
              <CardHeader>
                <CardTitle>Order Timeline</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {order.timeline.map((event, index) => (
                    <div key={index} className="flex items-start space-x-4">
                      <div
                        className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                          event.completed ? "bg-green-100 text-green-600" : "bg-gray-100 text-gray-400"
                        }`}
                      >
                        {event.completed ? <CheckCircle className="h-4 w-4" /> : <Clock className="h-4 w-4" />}
                      </div>
                      <div className="flex-1">
                        <h4 className={`font-semibold ${event.completed ? "text-gray-900" : "text-gray-500"}`}>
                          {event.status}
                        </h4>
                        <p className="text-sm text-gray-600">{event.description}</p>
                        <p className="text-xs text-gray-500 mt-1">{event.timestamp}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Delivery Person Info */}
            {order.deliveryPerson && order.status === "shipped" && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Truck className="h-5 w-5 mr-2" />
                    Delivery Information
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div>
                      <p className="font-semibold">{order.deliveryPerson.name}</p>
                      <p className="text-sm text-gray-600">{order.deliveryPerson.vehicle}</p>
                    </div>
                    <div className="flex space-x-2">
                      <Button size="sm" onClick={handleCallDelivery}>
                        <Phone className="h-4 w-4 mr-1" />
                        Call Delivery Person
                      </Button>
                      <Button size="sm" variant="outline" onClick={handleGetDirections}>
                        <Navigation className="h-4 w-4 mr-1" />
                        Track Location
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1 space-y-6">
            {/* Order Details */}
            <Card>
              <CardHeader>
                <CardTitle>Order Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-sm text-gray-600">Order Number</p>
                  <p className="font-semibold">{order.orderNumber}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Order Date</p>
                  <p className="font-semibold">{new Date(order.date).toLocaleDateString()}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Delivery Method</p>
                  <p className="font-semibold capitalize">{order.deliveryMethod}</p>
                </div>
                {order.deliveryAddress && (
                  <div>
                    <p className="text-sm text-gray-600">Delivery Address</p>
                    <p className="font-semibold">{order.deliveryAddress}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Pharmacy Info */}
            <Card>
              <CardHeader>
                <CardTitle>Pharmacy</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold">{order.pharmacy.name}</h4>
                  <p className="text-sm text-gray-600">{order.pharmacy.address}</p>
                  <div className="flex items-center mt-1">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400 mr-1" />
                    <span className="text-sm">{order.pharmacy.rating}</span>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Button size="sm" onClick={handleCallPharmacy}>
                    <Phone className="h-4 w-4 mr-1" />
                    Call
                  </Button>
                  <Button size="sm" variant="outline" onClick={handleGetDirections}>
                    <MapPin className="h-4 w-4 mr-1" />
                    Directions
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Order Items */}
            <Card>
              <CardHeader>
                <CardTitle>Items ({order.items.length})</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {order.items.map((item) => (
                    <div key={item.id} className="flex justify-between items-center">
                      <div className="flex-1">
                        <p className="font-medium text-sm">{item.name}</p>
                        <p className="text-xs text-gray-600">Qty: {item.quantity}</p>
                      </div>
                      <p className="font-semibold text-sm">{(item.price * item.quantity).toLocaleString()} FCFA</p>
                    </div>
                  ))}
                  <Separator />
                  <div className="flex justify-between items-center font-bold">
                    <span>Total</span>
                    <span>{order.total.toLocaleString()} FCFA</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-2">
                  {order.status === "delivered" && (
                    <Button className="w-full" onClick={handleReorder}>
                      <RotateCcw className="h-4 w-4 mr-2" />
                      Reorder Items
                    </Button>
                  )}
                  <Button variant="outline" className="w-full bg-transparent" asChild>
                    <Link href="/search">Continue Shopping</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
