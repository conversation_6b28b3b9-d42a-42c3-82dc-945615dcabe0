import { Routes, Route } from 'react-router-dom'
import Home from './pages/Home'
import Search from './pages/Search'
import Regions from './pages/Regions'
import Map from './pages/Map'
import Orders from './pages/Orders'
import Login from './pages/auth/Login'
import AdminDashboard from './pages/admin/Dashboard'
import AdminLayout from './components/AdminLayout'

function App() {
  return (
    <Routes>
      <Route path="/" element={<Home />} />
      <Route path="/search" element={<Search />} />
      <Route path="/regions" element={<Regions />} />
      <Route path="/map" element={<Map />} />
      <Route path="/orders" element={<Orders />} />
      <Route path="/auth/login" element={<Login />} />
      <Route path="/admin/*" element={
        <AdminLayout>
          <Routes>
            <Route path="/" element={<AdminDashboard />} />
          </Routes>
        </AdminLayout>
      } />
    </Routes>
  )
}

export default App
