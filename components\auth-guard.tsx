"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { getCurrentUser, isAdmin, type User } from "@/lib/auth"
import { Loader2 } from "lucide-react"

interface AuthGuardProps {
  children: React.ReactNode
  requireAdmin?: boolean
  requireAuth?: boolean
}

export function AuthGuard({ children, requireAdmin = false, requireAuth = true }: AuthGuardProps) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    const currentUser = getCurrentUser()
    setUser(currentUser)
    setLoading(false)

    if (requireAuth && !currentUser) {
      router.push("/auth/login")
      return
    }

    if (requireAdmin && !isAdmin(currentUser)) {
      router.push("/")
      return
    }
  }, [requireAuth, requireAdmin, router])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (requireAuth && !user) {
    return null
  }

  if (requireAdmin && !isAdmin(user)) {
    return null
  }

  return <>{children}</>
}
