"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { UserMenu } from "@/components/user-menu"
import { CartButton } from "@/components/cart-button"
import { Search, MapPin, Building2, Users, Phone, Navigation } from "lucide-react"
import Link from "next/link"
import { useState } from "react"
import { useRouter } from "next/navigation"
import { cameroonRegions } from "@/components/cameroon-data"

export default function RegionsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const router = useRouter()

  const filteredRegions = cameroonRegions.filter(
    (region) =>
      region.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      region.capital.toLowerCase().includes(searchQuery.toLowerCase()) ||
      region.cities.some((city) => city.toLowerCase().includes(searchQuery.toLowerCase())),
  )

  const handleFindPharmacies = (regionName: string) => {
    router.push(`/search?region=${regionName.toLowerCase()}`)
  }

  const handleViewOnMap = (regionName: string) => {
    router.push(`/map?region=${regionName.toLowerCase()}`)
  }

  const handleCallRegionalOffice = (regionName: string) => {
    // Mock phone numbers for regional offices
    const phoneNumbers: Record<string, string> = {
      Centre: "+237 222 20 30 40",
      Littoral: "+237 233 40 50 60",
      Ouest: "+237 233 44 50 60",
      "Nord-Ouest": "+237 233 36 40 50",
      "Sud-Ouest": "+237 233 32 40 50",
      Adamaoua: "+237 222 25 30 40",
      Est: "+237 222 24 30 40",
      "Extrême-Nord": "+237 222 29 30 40",
      Nord: "+237 222 27 30 40",
      Sud: "+237 222 28 30 40",
    }

    const phone = phoneNumbers[regionName] || "+237 222 20 30 40"
    window.open(`tel:${phone}`, "_self")
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="border-b bg-white sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="text-2xl font-bold text-blue-600">
            PharmaCare
          </Link>
          <div className="flex-1 max-w-2xl mx-8">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                type="text"
                placeholder="Search regions or cities..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 pr-4 py-2"
              />
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <CartButton />
            <UserMenu />
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-4">Pharmacies Across Cameroon</h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Find pharmacies in all 10 regions of Cameroon. From Douala to Maroua, we connect you with licensed
            pharmacies nationwide.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="text-center">
            <CardContent className="pt-6">
              <MapPin className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold">10</div>
              <div className="text-gray-600">Regions Covered</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <Building2 className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold">156</div>
              <div className="text-gray-600">Partner Pharmacies</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold">8.4K</div>
              <div className="text-gray-600">Active Users</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <Search className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold">5K+</div>
              <div className="text-gray-600">Medications Available</div>
            </CardContent>
          </Card>
        </div>

        {/* Regions Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredRegions.map((region) => (
            <Card key={region.name} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>{region.name}</span>
                  <Badge variant="secondary">{region.cities.length} cities</Badge>
                </CardTitle>
                <p className="text-gray-600">Capital: {region.capital}</p>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <h4 className="font-semibold text-sm mb-2">Major Cities:</h4>
                    <div className="flex flex-wrap gap-1">
                      {region.cities.slice(0, 4).map((city) => (
                        <Badge key={city} variant="outline" className="text-xs">
                          {city}
                        </Badge>
                      ))}
                      {region.cities.length > 4 && (
                        <Badge variant="outline" className="text-xs">
                          +{region.cities.length - 4} more
                        </Badge>
                      )}
                    </div>
                  </div>
                  <div className="flex justify-between items-center pt-2">
                    <div className="text-sm text-gray-600">
                      <span className="font-semibold">12-18</span> pharmacies
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button size="sm" className="flex-1" onClick={() => handleFindPharmacies(region.name)}>
                      <Search className="h-4 w-4 mr-1" />
                      Find Pharmacies
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => handleViewOnMap(region.name)}>
                      <MapPin className="h-4 w-4 mr-1" />
                      Map
                    </Button>
                  </div>
                  <Button
                    size="sm"
                    variant="outline"
                    className="w-full bg-transparent"
                    onClick={() => handleCallRegionalOffice(region.name)}
                  >
                    <Phone className="h-4 w-4 mr-1" />
                    Call Regional Office
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredRegions.length === 0 && (
          <div className="text-center py-12">
            <Search className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-600 mb-2">No regions found</h3>
            <p className="text-gray-500">Try adjusting your search terms</p>
          </div>
        )}

        {/* Popular Regions */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold mb-6">Most Popular Regions</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
              <CardContent className="pt-6">
                <h3 className="text-xl font-bold mb-2">Littoral (Douala)</h3>
                <p className="text-blue-100 mb-4">Economic capital with the highest number of pharmacies</p>
                <div className="flex justify-between items-center mb-4">
                  <span className="text-2xl font-bold">45+</span>
                  <span className="text-blue-100">Pharmacies</span>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="secondary"
                    size="sm"
                    className="flex-1"
                    onClick={() => handleFindPharmacies("Littoral")}
                  >
                    <Search className="h-4 w-4 mr-1" />
                    Explore
                  </Button>
                  <Button variant="secondary" size="sm" onClick={() => handleViewOnMap("Littoral")}>
                    <Navigation className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
              <CardContent className="pt-6">
                <h3 className="text-xl font-bold mb-2">Centre (Yaoundé)</h3>
                <p className="text-green-100 mb-4">Political capital with modern healthcare facilities</p>
                <div className="flex justify-between items-center mb-4">
                  <span className="text-2xl font-bold">38+</span>
                  <span className="text-green-100">Pharmacies</span>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="secondary"
                    size="sm"
                    className="flex-1"
                    onClick={() => handleFindPharmacies("Centre")}
                  >
                    <Search className="h-4 w-4 mr-1" />
                    Explore
                  </Button>
                  <Button variant="secondary" size="sm" onClick={() => handleViewOnMap("Centre")}>
                    <Navigation className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
              <CardContent className="pt-6">
                <h3 className="text-xl font-bold mb-2">Ouest (Bafoussam)</h3>
                <p className="text-purple-100 mb-4">Agricultural region with growing healthcare network</p>
                <div className="flex justify-between items-center mb-4">
                  <span className="text-2xl font-bold">22+</span>
                  <span className="text-purple-100">Pharmacies</span>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="secondary"
                    size="sm"
                    className="flex-1"
                    onClick={() => handleFindPharmacies("Ouest")}
                  >
                    <Search className="h-4 w-4 mr-1" />
                    Explore
                  </Button>
                  <Button variant="secondary" size="sm" onClick={() => handleViewOnMap("Ouest")}>
                    <Navigation className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
