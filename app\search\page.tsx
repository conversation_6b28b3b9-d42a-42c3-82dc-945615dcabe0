"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { UserMenu } from "@/components/user-menu"
import { CartButton } from "@/components/cart-button"
import { Search, MapPin, Star, Clock, Truck, Filter, Plus, Check } from "lucide-react"
import Link from "next/link"
import { useState } from "react"
import { useSearchParams } from "next/navigation"
import { cartManager } from "@/lib/cart"

interface Medication {
  id: string
  name: string
  genericName: string
  category: string
  description: string
  pharmacies: {
    id: string
    name: string
    price: number
    originalPrice?: number
    distance: number
    rating: number
    deliveryTime: string
    inStock: boolean
    address: string
  }[]
}

export default function SearchPage() {
  const searchParams = useSearchParams()
  const initialQuery = searchParams.get("q") || ""

  const [searchQuery, setSearchQuery] = useState(initialQuery)
  const [sortBy, setSortBy] = useState("price-low")
  const [addedItems, setAddedItems] = useState<Set<string>>(new Set())
  const [filters, setFilters] = useState({
    maxDistance: "",
    minRating: "",
    deliveryOnly: false,
    inStockOnly: true,
  })

  // Mock data - in real app, this would come from API
  const medications: Medication[] = [
    {
      id: "1",
      name: "Paracetamol 500mg",
      genericName: "Acetaminophen",
      category: "Pain Relief",
      description: "Effective pain relief and fever reducer",
      pharmacies: [
        {
          id: "p1",
          name: "Pharmacie du Centre",
          price: 2500,
          originalPrice: 3000,
          distance: 1.2,
          rating: 4.8,
          deliveryTime: "2-4 hours",
          inStock: true,
          address: "Avenue Kennedy, Douala",
        },
        {
          id: "p2",
          name: "Pharmacie Centrale",
          price: 2800,
          distance: 2.1,
          rating: 4.6,
          deliveryTime: "3-5 hours",
          inStock: true,
          address: "Rue de la Réunification, Yaoundé",
        },
        {
          id: "p3",
          name: "Pharmacie Moderne",
          price: 2300,
          distance: 0.8,
          rating: 4.7,
          deliveryTime: "1-3 hours",
          inStock: true,
          address: "Commercial Avenue, Bamenda",
        },
      ],
    },
    {
      id: "2",
      name: "Coartem (Artemether/Lumefantrine)",
      genericName: "Antimalarial",
      category: "Malaria Treatment",
      description: "WHO-recommended antimalarial treatment",
      pharmacies: [
        {
          id: "p1",
          name: "Pharmacie du Centre",
          price: 8500,
          distance: 1.2,
          rating: 4.8,
          deliveryTime: "2-4 hours",
          inStock: true,
          address: "Avenue Kennedy, Douala",
        },
        {
          id: "p4",
          name: "Pharmacie de l'Espoir",
          price: 7800,
          originalPrice: 9000,
          distance: 1.8,
          rating: 4.5,
          deliveryTime: "2-4 hours",
          inStock: true,
          address: "Quartier Mvog-Ada, Yaoundé",
        },
      ],
    },
    {
      id: "3",
      name: "Amoxicillin 500mg",
      genericName: "Amoxicillin",
      category: "Antibiotic",
      description: "Broad-spectrum antibiotic for bacterial infections",
      pharmacies: [
        {
          id: "p2",
          name: "Pharmacie Centrale",
          price: 4500,
          distance: 2.1,
          rating: 4.6,
          deliveryTime: "3-5 hours",
          inStock: true,
          address: "Rue de la Réunification, Yaoundé",
        },
        {
          id: "p5",
          name: "Pharmacie Saint-Michel",
          price: 4200,
          distance: 3.2,
          rating: 4.4,
          deliveryTime: "4-6 hours",
          inStock: false,
          address: "Rue des Palmiers, Bafoussam",
        },
      ],
    },
  ]

  const filteredMedications = medications.filter(
    (med) =>
      med.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      med.genericName.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const handleAddToCart = (medicationId: string, pharmacyId: string) => {
    const medication = medications.find((m) => m.id === medicationId)
    const pharmacy = medication?.pharmacies.find((p) => p.id === pharmacyId)

    if (medication && pharmacy && pharmacy.inStock) {
      cartManager.addItem({
        medicationId: medication.id,
        medicationName: medication.name,
        pharmacyId: pharmacy.id,
        pharmacyName: pharmacy.name,
        price: pharmacy.price,
        quantity: 1,
        inStock: pharmacy.inStock,
      })

      // Show visual feedback
      const itemKey = `${medicationId}-${pharmacyId}`
      setAddedItems((prev) => new Set(prev).add(itemKey))

      // Remove visual feedback after 2 seconds
      setTimeout(() => {
        setAddedItems((prev) => {
          const newSet = new Set(prev)
          newSet.delete(itemKey)
          return newSet
        })
      }, 2000)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="border-b bg-white sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="text-2xl font-bold text-blue-600">
            PharmaCare
          </Link>
          <div className="flex-1 max-w-2xl mx-8">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                type="text"
                placeholder="Search medications..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 pr-4 py-2"
              />
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <CartButton />
            <UserMenu />
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Filters Sidebar */}
          <div className="lg:w-64 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Filter className="mr-2 h-5 w-5" />
                  Filters
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="distance">Max Distance</Label>
                  <Select
                    value={filters.maxDistance}
                    onValueChange={(value) => setFilters({ ...filters, maxDistance: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Any distance" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">Within 1 km</SelectItem>
                      <SelectItem value="5">Within 5 km</SelectItem>
                      <SelectItem value="10">Within 10 km</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="rating">Minimum Rating</Label>
                  <Select
                    value={filters.minRating}
                    onValueChange={(value) => setFilters({ ...filters, minRating: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Any rating" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="4">4+ Stars</SelectItem>
                      <SelectItem value="4.5">4.5+ Stars</SelectItem>
                      <SelectItem value="4.8">4.8+ Stars</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="delivery"
                      checked={filters.deliveryOnly}
                      onCheckedChange={(checked) => setFilters({ ...filters, deliveryOnly: checked as boolean })}
                    />
                    <Label htmlFor="delivery">Delivery available</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="instock"
                      checked={filters.inStockOnly}
                      onCheckedChange={(checked) => setFilters({ ...filters, inStockOnly: checked as boolean })}
                    />
                    <Label htmlFor="instock">In stock only</Label>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Results */}
          <div className="flex-1">
            <div className="flex items-center justify-between mb-6">
              <h1 className="text-2xl font-bold">
                {searchQuery ? `Results for "${searchQuery}"` : "All Medications"}
                <span className="text-gray-500 text-lg ml-2">({filteredMedications.length} found)</span>
              </h1>
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="price-low">Price: Low to High</SelectItem>
                  <SelectItem value="price-high">Price: High to Low</SelectItem>
                  <SelectItem value="distance">Distance</SelectItem>
                  <SelectItem value="rating">Rating</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-6">
              {filteredMedications.map((medication) => (
                <Card key={medication.id} className="overflow-hidden">
                  <CardHeader className="bg-gray-50">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-xl">{medication.name}</CardTitle>
                        <p className="text-gray-600">{medication.genericName}</p>
                        <Badge variant="secondary" className="mt-2">
                          {medication.category}
                        </Badge>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-600">Starting from</p>
                        <p className="text-2xl font-bold text-green-600">
                          {Math.min(...medication.pharmacies.map((p) => p.price)).toLocaleString()} FCFA
                        </p>
                      </div>
                    </div>
                    <p className="text-gray-600 mt-2">{medication.description}</p>
                  </CardHeader>
                  <CardContent className="p-0">
                    <div className="space-y-0">
                      {medication.pharmacies.map((pharmacy) => {
                        const itemKey = `${medication.id}-${pharmacy.id}`
                        const isAdded = addedItems.has(itemKey)

                        return (
                          <div key={pharmacy.id} className="border-b last:border-b-0 p-4 hover:bg-gray-50">
                            <div className="flex items-center justify-between">
                              <div className="flex-1">
                                <div className="flex items-center space-x-4">
                                  <div>
                                    <h4 className="font-semibold">{pharmacy.name}</h4>
                                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                                      <MapPin className="h-4 w-4" />
                                      <span>{pharmacy.distance} km away</span>
                                      <span>•</span>
                                      <div className="flex items-center">
                                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                                        <span className="ml-1">{pharmacy.rating}</span>
                                      </div>
                                    </div>
                                    <p className="text-sm text-gray-500">{pharmacy.address}</p>
                                  </div>
                                </div>
                              </div>

                              <div className="flex items-center space-x-4">
                                <div className="text-center">
                                  <div className="flex items-center text-sm text-gray-600">
                                    <Clock className="h-4 w-4 mr-1" />
                                    {pharmacy.deliveryTime}
                                  </div>
                                  <div className="flex items-center text-sm text-green-600 mt-1">
                                    <Truck className="h-4 w-4 mr-1" />
                                    Delivery available
                                  </div>
                                </div>

                                <div className="text-right">
                                  <div className="flex items-center space-x-2">
                                    {pharmacy.originalPrice && (
                                      <span className="text-sm text-gray-500 line-through">
                                        {pharmacy.originalPrice.toLocaleString()} FCFA
                                      </span>
                                    )}
                                    <span className="text-xl font-bold">{pharmacy.price.toLocaleString()} FCFA</span>
                                  </div>
                                  <div className="mt-2">
                                    {pharmacy.inStock ? (
                                      <Button
                                        size="sm"
                                        onClick={() => handleAddToCart(medication.id, pharmacy.id)}
                                        className={isAdded ? "bg-green-600 hover:bg-green-700" : ""}
                                      >
                                        {isAdded ? (
                                          <>
                                            <Check className="h-4 w-4 mr-1" />
                                            Added
                                          </>
                                        ) : (
                                          <>
                                            <Plus className="h-4 w-4 mr-1" />
                                            Add to Cart
                                          </>
                                        )}
                                      </Button>
                                    ) : (
                                      <Button size="sm" variant="outline" disabled>
                                        Out of Stock
                                      </Button>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {filteredMedications.length === 0 && (
              <div className="text-center py-12">
                <Search className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-600 mb-2">No medications found</h3>
                <p className="text-gray-500">Try adjusting your search terms or filters</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
