"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ShoppingCart } from "lucide-react"
import { cartManager, type <PERSON><PERSON> } from "@/lib/cart"
import Link from "next/link"

export function CartButton() {
  const [cart, setCart] = useState<Cart>({ items: [], total: 0, itemCount: 0 })

  useEffect(() => {
    setCart(cartManager.getCart())
    const unsubscribe = cartManager.subscribe(setCart)
    return unsubscribe
  }, [])

  return (
    <Button variant="outline" size="icon" className="relative bg-transparent" asChild>
      <Link href="/cart">
        <ShoppingCart className="h-5 w-5" />
        {cart.itemCount > 0 && (
          <Badge className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs">
            {cart.itemCount > 99 ? "99+" : cart.itemCount}
          </Badge>
        )}
      </Link>
    </Button>
  )
}
