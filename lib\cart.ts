// Shopping cart management
export interface CartItem {
  id: string
  medicationId: string
  medicationName: string
  pharmacyId: string
  pharmacyName: string
  price: number
  quantity: number
  image?: string
  inStock: boolean
}

export interface Cart {
  items: CartItem[]
  total: number
  itemCount: number
}

class CartManager {
  private static instance: CartManager
  private cart: Cart = { items: [], total: 0, itemCount: 0 }
  private listeners: ((cart: Cart) => void)[] = []

  static getInstance(): CartManager {
    if (!CartManager.instance) {
      CartManager.instance = new CartManager()
    }
    return CartManager.instance
  }

  constructor() {
    if (typeof window !== "undefined") {
      this.loadCart()
    }
  }

  private loadCart() {
    const savedCart = localStorage.getItem("pharmacare_cart")
    if (savedCart) {
      try {
        this.cart = JSON.parse(savedCart)
        this.calculateTotals()
      } catch (error) {
        console.error("Error loading cart:", error)
        this.cart = { items: [], total: 0, itemCount: 0 }
      }
    }
  }

  private saveCart() {
    if (typeof window !== "undefined") {
      localStorage.setItem("pharmacare_cart", JSON.stringify(this.cart))
    }
  }

  private calculateTotals() {
    this.cart.total = this.cart.items.reduce((sum, item) => sum + item.price * item.quantity, 0)
    this.cart.itemCount = this.cart.items.reduce((sum, item) => sum + item.quantity, 0)
  }

  private notifyListeners() {
    this.listeners.forEach((listener) => listener(this.cart))
  }

  addItem(item: Omit<CartItem, "id">): void {
    const existingItemIndex = this.cart.items.findIndex(
      (cartItem) => cartItem.medicationId === item.medicationId && cartItem.pharmacyId === item.pharmacyId,
    )

    if (existingItemIndex >= 0) {
      this.cart.items[existingItemIndex].quantity += item.quantity
    } else {
      const newItem: CartItem = {
        ...item,
        id: `${item.medicationId}-${item.pharmacyId}-${Date.now()}`,
      }
      this.cart.items.push(newItem)
    }

    this.calculateTotals()
    this.saveCart()
    this.notifyListeners()
  }

  removeItem(itemId: string): void {
    this.cart.items = this.cart.items.filter((item) => item.id !== itemId)
    this.calculateTotals()
    this.saveCart()
    this.notifyListeners()
  }

  updateQuantity(itemId: string, quantity: number): void {
    const item = this.cart.items.find((item) => item.id === itemId)
    if (item) {
      if (quantity <= 0) {
        this.removeItem(itemId)
      } else {
        item.quantity = quantity
        this.calculateTotals()
        this.saveCart()
        this.notifyListeners()
      }
    }
  }

  clearCart(): void {
    this.cart = { items: [], total: 0, itemCount: 0 }
    this.saveCart()
    this.notifyListeners()
  }

  getCart(): Cart {
    return { ...this.cart }
  }

  subscribe(listener: (cart: Cart) => void): () => void {
    this.listeners.push(listener)
    return () => {
      this.listeners = this.listeners.filter((l) => l !== listener)
    }
  }
}

export const cartManager = CartManager.getInstance()
