"use client"

import type React from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { UserMenu } from "@/components/user-menu"
import { Search, MapPin, Clock, Shield, ShoppingCart } from "lucide-react"
import Link from "next/link"
import { useState } from "react"
import { useRouter } from "next/navigation"

export default function HomePage() {
  const [searchQuery, setSearchQuery] = useState("")
  const router = useRouter()

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery)}`)
    }
  }

  const popularMedications = ["Paracetamol", "Chloroquine", "Amoxicillin", "Artemether", "Coartem", "Flagyl"]

  const features = [
    {
      icon: Search,
      title: "Smart Search",
      description: "Find medications across multiple pharmacies instantly",
    },
    {
      icon: MapPin,
      title: "Location-Based",
      description: "Discover nearby pharmacies with real-time availability",
    },
    {
      icon: Clock,
      title: "Quick Delivery",
      description: "Same-day delivery or convenient pickup options",
    },
    {
      icon: Shield,
      title: "Secure & Safe",
      description: "Licensed pharmacies with verified medications",
    },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="text-2xl font-bold text-blue-600">
            PharmaCare
          </Link>
          <nav className="hidden md:flex items-center space-x-6">
            <Link href="/search" className="text-gray-600 hover:text-blue-600">
              Search
            </Link>
            <Link href="/regions" className="text-gray-600 hover:text-blue-600">
              Regions
            </Link>
            <Link href="/map" className="text-gray-600 hover:text-blue-600">
              Map
            </Link>
            <Link href="/orders" className="text-gray-600 hover:text-blue-600">
              Orders
            </Link>
          </nav>
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="icon">
              <ShoppingCart className="h-5 w-5" />
            </Button>
            <UserMenu />
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="container mx-auto px-4 py-16 text-center">
        <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
          Find Your Medications
          <span className="text-blue-600 block">Across Cameroon</span>
        </h1>
        <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
          Search across pharmacies in Douala, Yaoundé, Bamenda, and other cities. Compare prices and get your
          medications delivered nationwide.
        </p>

        {/* Search Bar */}
        <form onSubmit={handleSearch} className="max-w-2xl mx-auto mb-8">
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              type="text"
              placeholder="Search for medications (e.g., Paracetamol, Ibuprofen)"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-12 pr-4 py-4 text-lg rounded-full border-2 border-gray-200 focus:border-blue-500"
            />
            <Button type="submit" className="absolute right-2 top-1/2 transform -translate-y-1/2 rounded-full px-8">
              Search
            </Button>
          </div>
        </form>

        {/* Popular Searches */}
        <div className="mb-12">
          <p className="text-gray-600 mb-4">Popular searches:</p>
          <div className="flex flex-wrap justify-center gap-2">
            {popularMedications.map((med) => (
              <Badge
                key={med}
                variant="secondary"
                className="cursor-pointer hover:bg-blue-100"
                onClick={() => {
                  setSearchQuery(med)
                  router.push(`/search?q=${encodeURIComponent(med)}`)
                }}
              >
                {med}
              </Badge>
            ))}
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="container mx-auto px-4 py-16">
        <h2 className="text-3xl font-bold text-center mb-12">Why Choose PharmaCare?</h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <Card key={index} className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <feature.icon className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <CardTitle>{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>{feature.description}</CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Stats */}
      <section className="bg-blue-600 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold mb-2">200+</div>
              <div className="text-blue-100">Partner Pharmacies</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">5K+</div>
              <div className="text-blue-100">Medications Available</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">15K+</div>
              <div className="text-blue-100">Happy Customers</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">10</div>
              <div className="text-blue-100">Regions Covered</div>
            </div>
          </div>
        </div>
      </section>

      {/* Regions Section */}
      <section className="container mx-auto px-4 py-16">
        <h2 className="text-3xl font-bold text-center mb-12">Coverage Across Cameroon</h2>
        <div className="grid md:grid-cols-3 gap-8 mb-8">
          <Card className="text-center hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="text-blue-600">Littoral & Centre</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">
                Major cities including Douala and Yaoundé with extensive pharmacy networks
              </p>
              <Button className="mt-4 bg-transparent" variant="outline" asChild>
                <Link href="/search?region=littoral,centre">Explore</Link>
              </Button>
            </CardContent>
          </Card>
          <Card className="text-center hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="text-blue-600">Northern Regions</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Serving Adamaoua, Nord, and Extrême-Nord with reliable delivery</p>
              <Button className="mt-4 bg-transparent" variant="outline" asChild>
                <Link href="/search?region=nord">Explore</Link>
              </Button>
            </CardContent>
          </Card>
          <Card className="text-center hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="text-blue-600">Western Regions</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Comprehensive coverage in Ouest, Nord-Ouest, and Sud-Ouest</p>
              <Button className="mt-4 bg-transparent" variant="outline" asChild>
                <Link href="/search?region=ouest">Explore</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
        <div className="text-center">
          <Button size="lg" asChild>
            <Link href="/regions">
              <MapPin className="mr-2 h-5 w-5" />
              View All Regions
            </Link>
          </Button>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-4 py-16 text-center">
        <h2 className="text-3xl font-bold mb-6">Ready to Get Started?</h2>
        <p className="text-xl text-gray-600 mb-8">
          Join thousands of customers who trust PharmaCare for their medication needs.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button size="lg" asChild>
            <Link href="/search">
              <Search className="mr-2 h-5 w-5" />
              Start Searching
            </Link>
          </Button>
          <Button size="lg" variant="outline" asChild>
            <Link href="/map">
              <MapPin className="mr-2 h-5 w-5" />
              Find Pharmacies
            </Link>
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">PharmaCare</h3>
              <p className="text-gray-400">Your trusted partner for finding and ordering medications online.</p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Services</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link href="/search" className="hover:text-white">
                    Medication Search
                  </Link>
                </li>
                <li>
                  <Link href="/map" className="hover:text-white">
                    Pharmacy Locator
                  </Link>
                </li>
                <li>
                  <Link href="/delivery" className="hover:text-white">
                    Home Delivery
                  </Link>
                </li>
                <li>
                  <Link href="/pickup" className="hover:text-white">
                    Store Pickup
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link href="/help" className="hover:text-white">
                    Help Center
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="hover:text-white">
                    Contact Us
                  </Link>
                </li>
                <li>
                  <Link href="/track" className="hover:text-white">
                    Track Order
                  </Link>
                </li>
                <li>
                  <Link href="/returns" className="hover:text-white">
                    Returns
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Company</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link href="/about" className="hover:text-white">
                    About Us
                  </Link>
                </li>
                <li>
                  <Link href="/careers" className="hover:text-white">
                    Careers
                  </Link>
                </li>
                <li>
                  <Link href="/privacy" className="hover:text-white">
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link href="/terms" className="hover:text-white">
                    Terms of Service
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 PharmaCare. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
