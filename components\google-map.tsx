"use client"

import { useEffect, useRef, useState } from "react"
import { loadGoogleMapsAPI, type PharmacyLocation, type MapLocation, google } from "@/lib/google-maps"
import { Loader2, MapPin } from "lucide-react"

interface GoogleMapProps {
  pharmacies: PharmacyLocation[]
  userLocation?: MapLocation | null
  onPharmacySelect?: (pharmacy: PharmacyLocation) => void
  className?: string
}

export function GoogleMap({ pharmacies, userLocation, onPharmacySelect, className }: GoogleMapProps) {
  const mapRef = useRef<HTMLDivElement>(null)
  const mapInstanceRef = useRef<google.maps.Map | null>(null)
  const markersRef = useRef<google.maps.Marker[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const initializeMap = async () => {
      try {
        setIsLoading(true)
        setError(null)

        // Load Google Maps API
        await loadGoogleMapsAPI()

        if (!mapRef.current) return

        // Default center (Cameroon)
        const defaultCenter = { lat: 4.0511, lng: 9.7679 } // Douala

        // Create map
        const map = new google.maps.Map(mapRef.current, {
          zoom: userLocation ? 12 : 6,
          center: userLocation || defaultCenter,
          mapTypeId: google.maps.MapTypeId.ROADMAP,
          styles: [
            {
              featureType: "poi.business",
              stylers: [{ visibility: "off" }],
            },
          ],
        })

        mapInstanceRef.current = map

        // Clear existing markers
        markersRef.current.forEach((marker) => marker.setMap(null))
        markersRef.current = []

        // Add user location marker
        if (userLocation) {
          const userMarker = new google.maps.Marker({
            position: userLocation,
            map: map,
            title: "Your Location",
            icon: {
              url:
                "data:image/svg+xml;charset=UTF-8," +
                encodeURIComponent(`
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="12" cy="12" r="8" fill="#3B82F6" stroke="#FFFFFF" strokeWidth="2"/>
                  <circle cx="12" cy="12" r="3" fill="#FFFFFF"/>
                </svg>
              `),
              scaledSize: new google.maps.Size(24, 24),
              anchor: new google.maps.Point(12, 12),
            },
          })
          markersRef.current.push(userMarker)
        }

        // Add pharmacy markers
        pharmacies.forEach((pharmacy) => {
          const marker = new google.maps.Marker({
            position: { lat: pharmacy.lat, lng: pharmacy.lng },
            map: map,
            title: pharmacy.name,
            icon: {
              url:
                "data:image/svg+xml;charset=UTF-8," +
                encodeURIComponent(`
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z" fill="${pharmacy.isOpen ? "#10B981" : "#EF4444"}" stroke="#FFFFFF" strokeWidth="1"/>
                  <path d="M12 6v6M9 9h6" stroke="#FFFFFF" strokeWidth="2" strokeLinecap="round"/>
                </svg>
              `),
              scaledSize: new google.maps.Size(32, 32),
              anchor: new google.maps.Point(16, 32),
            },
          })

          // Create info window
          const infoWindow = new google.maps.InfoWindow({
            content: `
              <div style="padding: 8px; min-width: 200px;">
                <h3 style="margin: 0 0 8px 0; font-size: 16px; font-weight: bold;">${pharmacy.name}</h3>
                <p style="margin: 0 0 4px 0; font-size: 14px; color: #666;">${pharmacy.address}</p>
                <p style="margin: 0 0 4px 0; font-size: 14px; color: #666;">${pharmacy.phone}</p>
                <div style="display: flex; align-items: center; margin: 4px 0;">
                  <span style="color: #F59E0B; margin-right: 4px;">★</span>
                  <span style="font-size: 14px;">${pharmacy.rating}</span>
                  <span style="margin-left: 8px; padding: 2px 6px; border-radius: 4px; font-size: 12px; background: ${pharmacy.isOpen ? "#D1FAE5" : "#FEE2E2"}; color: ${pharmacy.isOpen ? "#065F46" : "#991B1B"};">
                    ${pharmacy.isOpen ? "Open" : "Closed"}
                  </span>
                </div>
                <button onclick="window.selectPharmacy('${pharmacy.id}')" style="margin-top: 8px; padding: 4px 12px; background: #3B82F6; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                  View Details
                </button>
              </div>
            `,
          })

          marker.addListener("click", () => {
            infoWindow.open(map, marker)
          })

          markersRef.current.push(marker)
        })

        // Global function for info window buttons
        ;(window as any).selectPharmacy = (pharmacyId: string) => {
          const pharmacy = pharmacies.find((p) => p.id === pharmacyId)
          if (pharmacy && onPharmacySelect) {
            onPharmacySelect(pharmacy)
          }
        }

        setIsLoading(false)
      } catch (err) {
        console.error("Error initializing map:", err)
        setError("Failed to load map. Please check your internet connection.")
        setIsLoading(false)
      }
    }

    initializeMap()

    return () => {
      // Cleanup markers
      markersRef.current.forEach((marker) => marker.setMap(null))
      markersRef.current = []
    }
  }, [pharmacies, userLocation, onPharmacySelect])

  if (error) {
    return (
      <div className={`flex items-center justify-center bg-gray-100 rounded-lg ${className}`}>
        <div className="text-center p-8">
          <MapPin className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-600 mb-2">Map Error</h3>
          <p className="text-gray-500">{error}</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`relative ${className}`}>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg z-10">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-2" />
            <p className="text-gray-600">Loading map...</p>
          </div>
        </div>
      )}
      <div ref={mapRef} className={`w-full h-full rounded-lg ${className}`} />
    </div>
  )
}
