// Simple authentication utilities
export interface User {
  id: string
  email: string
  name: string
  role: "admin" | "pharmacy" | "customer"
  region?: string
}

// Mock user database - in production, this would be in a real database
const mockUsers: Record<string, { password: string; user: User }> = {
  "<EMAIL>": {
    password: "admin123",
    user: {
      id: "admin-1",
      email: "<EMAIL>",
      name: "System Administrator",
      role: "admin",
    },
  },
  "<EMAIL>": {
    password: "pharmacy123",
    user: {
      id: "pharmacy-1",
      email: "<EMAIL>",
      name: "Pharmacie du Centre",
      role: "pharmacy",
      region: "Littoral",
    },
  },
  "<EMAIL>": {
    password: "user123",
    user: {
      id: "user-1",
      email: "<EMAIL>",
      name: "<PERSON>",
      role: "customer",
      region: "Littoral",
    },
  },
}

export const authenticateUser = (email: string, password: string): User | null => {
  const userRecord = mockUsers[email]
  if (userRecord && userRecord.password === password) {
    return userRecord.user
  }
  return null
}

export const isAdmin = (user: User | null): boolean => {
  return user?.role === "admin"
}

export const isPharmacy = (user: User | null): boolean => {
  return user?.role === "pharmacy"
}

export const getCurrentUser = (): User | null => {
  if (typeof window === "undefined") return null

  const userStr = localStorage.getItem("currentUser")
  if (userStr) {
    try {
      return JSON.parse(userStr)
    } catch {
      return null
    }
  }
  return null
}

export const setCurrentUser = (user: User | null): void => {
  if (typeof window === "undefined") return

  if (user) {
    localStorage.setItem("currentUser", JSON.stringify(user))
  } else {
    localStorage.removeItem("currentUser")
  }
}

export const logout = (): void => {
  if (typeof window === "undefined") return
  localStorage.removeItem("currentUser")
}
