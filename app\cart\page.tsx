"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { UserMenu } from "@/components/user-menu"
import { Minus, Plus, Trash2, ShoppingBag, ArrowLeft } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { cartManager, type Cart, type CartItem } from "@/lib/cart"

export default function CartPage() {
  const [cart, setCart] = useState<Cart>({ items: [], total: 0, itemCount: 0 })
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  useEffect(() => {
    setCart(cartManager.getCart())
    const unsubscribe = cartManager.subscribe(setCart)
    return unsubscribe
  }, [])

  const handleUpdateQuantity = (itemId: string, newQuantity: number) => {
    cartManager.updateQuantity(itemId, newQuantity)
  }

  const handleRemoveItem = (itemId: string) => {
    cartManager.removeItem(itemId)
  }

  const handleClearCart = () => {
    cartManager.clearCart()
  }

  const handleCheckout = () => {
    if (cart.items.length === 0) return
    setIsLoading(true)
    // Simulate checkout process
    setTimeout(() => {
      router.push("/checkout")
      setIsLoading(false)
    }, 1000)
  }

  const groupedItems = cart.items.reduce(
    (groups, item) => {
      if (!groups[item.pharmacyId]) {
        groups[item.pharmacyId] = {
          pharmacyName: item.pharmacyName,
          items: [],
          subtotal: 0,
        }
      }
      groups[item.pharmacyId].items.push(item)
      groups[item.pharmacyId].subtotal += item.price * item.quantity
      return groups
    },
    {} as Record<string, { pharmacyName: string; items: CartItem[]; subtotal: number }>,
  )

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="border-b bg-white sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="text-2xl font-bold text-blue-600">
            PharmaCare
          </Link>
          <div className="flex items-center space-x-4">
            <Button variant="outline" asChild>
              <Link href="/search">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Continue Shopping
              </Link>
            </Button>
            <UserMenu />
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold">Shopping Cart</h1>
          {cart.items.length > 0 && (
            <Button variant="outline" onClick={handleClearCart}>
              <Trash2 className="h-4 w-4 mr-2" />
              Clear Cart
            </Button>
          )}
        </div>

        {cart.items.length === 0 ? (
          <div className="text-center py-16">
            <ShoppingBag className="h-24 w-24 text-gray-400 mx-auto mb-6" />
            <h2 className="text-2xl font-semibold text-gray-600 mb-4">Your cart is empty</h2>
            <p className="text-gray-500 mb-8">Add some medications to get started</p>
            <Button size="lg" asChild>
              <Link href="/search">Start Shopping</Link>
            </Button>
          </div>
        ) : (
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Cart Items */}
            <div className="lg:col-span-2 space-y-6">
              {Object.entries(groupedItems).map(([pharmacyId, group]) => (
                <Card key={pharmacyId}>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>{group.pharmacyName}</span>
                      <Badge variant="secondary">{group.items.length} items</Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {group.items.map((item) => (
                      <div key={item.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                        <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                          <ShoppingBag className="h-8 w-8 text-gray-400" />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold">{item.medicationName}</h3>
                          <p className="text-sm text-gray-600">{item.pharmacyName}</p>
                          <p className="text-lg font-bold text-green-600">{item.price.toLocaleString()} FCFA</p>
                          {!item.inStock && <Badge variant="destructive">Out of Stock</Badge>}
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleUpdateQuantity(item.id, item.quantity - 1)}
                            disabled={item.quantity <= 1}
                          >
                            <Minus className="h-4 w-4" />
                          </Button>
                          <Input
                            type="number"
                            value={item.quantity}
                            onChange={(e) => handleUpdateQuantity(item.id, Number.parseInt(e.target.value) || 1)}
                            className="w-16 text-center"
                            min="1"
                          />
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleUpdateQuantity(item.id, item.quantity + 1)}
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold">{(item.price * item.quantity).toLocaleString()} FCFA</p>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleRemoveItem(item.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                    <Separator />
                    <div className="flex justify-between items-center font-semibold">
                      <span>Pharmacy Subtotal:</span>
                      <span>{group.subtotal.toLocaleString()} FCFA</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <Card className="sticky top-24">
                <CardHeader>
                  <CardTitle>Order Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span>Items ({cart.itemCount}):</span>
                    <span>{cart.total.toLocaleString()} FCFA</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Delivery:</span>
                    <span className="text-green-600">Free</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between text-lg font-bold">
                    <span>Total:</span>
                    <span>{cart.total.toLocaleString()} FCFA</span>
                  </div>
                  <Button className="w-full" size="lg" onClick={handleCheckout} disabled={isLoading}>
                    {isLoading ? "Processing..." : "Proceed to Checkout"}
                  </Button>
                  <div className="text-center text-sm text-gray-600">
                    <p>Free delivery on orders over 10,000 FCFA</p>
                    <p>Secure checkout with SSL encryption</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
