"use client"

import type React from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { UserMenu } from "@/components/user-menu"
import { CartButton } from "@/components/cart-button"
import { Package, Clock, CheckCircle, XCircle, Search, Eye, RotateCcw, Truck, Phone } from "lucide-react"
import Link from "next/link"
import { useState } from "react"

interface Order {
  id: string
  orderNumber: string
  date: string
  status: "pending" | "confirmed" | "shipped" | "delivered" | "cancelled"
  total: number
  pharmacy: {
    name: string
    address: string
    phone: string
  }
  items: {
    name: string
    quantity: number
    price: number
  }[]
  deliveryMethod: "delivery" | "pickup"
  estimatedDelivery?: string
  trackingNumber?: string
}

export default function OrdersPage() {
  const [searchQuery, setSearchQuery] = useState("")

  // Mock orders data
  const orders: Order[] = [
    {
      id: "1",
      orderNumber: "ORD-2024-001",
      date: "2024-01-15",
      status: "delivered",
      total: 18500,
      pharmacy: {
        name: "Pharmacie du Centre",
        address: "Avenue Kennedy, Douala",
        phone: "+237 233 42 15 67",
      },
      items: [
        { name: "Paracetamol 500mg", quantity: 2, price: 2500 },
        { name: "Coartem", quantity: 1, price: 8500 },
      ],
      deliveryMethod: "delivery",
      estimatedDelivery: "2024-01-16",
      trackingNumber: "TRK123456789",
    },
    {
      id: "2",
      orderNumber: "ORD-2024-002",
      date: "2024-01-20",
      status: "shipped",
      total: 12300,
      pharmacy: {
        name: "Pharmacie Centrale",
        address: "Rue de la Réunification, Yaoundé",
        phone: "+237 222 23 45 78",
      },
      items: [
        { name: "Vitamin D3 1000IU", quantity: 1, price: 9500 },
        { name: "Multivitamin", quantity: 1, price: 2800 },
      ],
      deliveryMethod: "delivery",
      estimatedDelivery: "2024-01-22",
      trackingNumber: "TRK987654321",
    },
    {
      id: "3",
      orderNumber: "ORD-2024-003",
      date: "2024-01-25",
      status: "pending",
      total: 13000,
      pharmacy: {
        name: "Pharmacie de l'Espoir",
        address: "Quartier Mvog-Ada, Yaoundé",
        phone: "+237 222 15 78 90",
      },
      items: [
        { name: "Amoxicillin 500mg", quantity: 1, price: 4500 },
        { name: "Flagyl 500mg", quantity: 1, price: 8500 },
      ],
      deliveryMethod: "pickup",
    },
    {
      id: "4",
      orderNumber: "ORD-2024-004",
      date: "2024-01-28",
      status: "cancelled",
      total: 3200,
      pharmacy: {
        name: "Pharmacie Moderne",
        address: "Commercial Avenue, Bamenda",
        phone: "+237 233 36 24 89",
      },
      items: [{ name: "Aspirin 325mg", quantity: 1, price: 3200 }],
      deliveryMethod: "delivery",
    },
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4" />
      case "confirmed":
        return <Package className="h-4 w-4" />
      case "shipped":
        return <Truck className="h-4 w-4" />
      case "delivered":
        return <CheckCircle className="h-4 w-4" />
      case "cancelled":
        return <XCircle className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800"
      case "confirmed":
        return "bg-blue-100 text-blue-800"
      case "shipped":
        return "bg-purple-100 text-purple-800"
      case "delivered":
        return "bg-green-100 text-green-800"
      case "cancelled":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const filteredOrders = orders.filter(
    (order) =>
      order.orderNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
      order.pharmacy.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      order.items.some((item) => item.name.toLowerCase().includes(searchQuery.toLowerCase())),
  )

  const getOrdersByStatus = (status: string) => {
    if (status === "all") return filteredOrders
    return filteredOrders.filter((order) => order.status === status)
  }

  const handleTrackOrder = (trackingNumber: string) => {
    // In a real app, this would open a tracking page or modal
    alert(`Tracking order: ${trackingNumber}`)
  }

  const handleReorder = (orderId: string) => {
    // In a real app, this would add items back to cart
    alert(`Reordering items from order: ${orderId}`)
  }

  const handleCallPharmacy = (phone: string) => {
    window.open(`tel:${phone}`, "_self")
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="border-b bg-white sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="text-2xl font-bold text-blue-600">
            PharmaCare
          </Link>
          <nav className="hidden md:flex items-center space-x-6">
            <Link href="/search" className="text-gray-600 hover:text-blue-600">
              Search
            </Link>
            <Link href="/map" className="text-gray-600 hover:text-blue-600">
              Map
            </Link>
            <Link href="/orders" className="text-blue-600 font-semibold">
              Orders
            </Link>
          </nav>
          <div className="flex items-center space-x-4">
            <CartButton />
            <UserMenu />
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold">My Orders</h1>
          <div className="relative w-64">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search orders..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <Tabs defaultValue="all" className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="all">All Orders</TabsTrigger>
            <TabsTrigger value="pending">Pending</TabsTrigger>
            <TabsTrigger value="confirmed">Confirmed</TabsTrigger>
            <TabsTrigger value="shipped">Shipped</TabsTrigger>
            <TabsTrigger value="delivered">Delivered</TabsTrigger>
            <TabsTrigger value="cancelled">Cancelled</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4">
            {getOrdersByStatus("all").map((order) => (
              <OrderCard
                key={order.id}
                order={order}
                getStatusIcon={getStatusIcon}
                getStatusColor={getStatusColor}
                onTrackOrder={handleTrackOrder}
                onReorder={handleReorder}
                onCallPharmacy={handleCallPharmacy}
              />
            ))}
          </TabsContent>

          <TabsContent value="pending" className="space-y-4">
            {getOrdersByStatus("pending").map((order) => (
              <OrderCard
                key={order.id}
                order={order}
                getStatusIcon={getStatusIcon}
                getStatusColor={getStatusColor}
                onTrackOrder={handleTrackOrder}
                onReorder={handleReorder}
                onCallPharmacy={handleCallPharmacy}
              />
            ))}
          </TabsContent>

          <TabsContent value="confirmed" className="space-y-4">
            {getOrdersByStatus("confirmed").map((order) => (
              <OrderCard
                key={order.id}
                order={order}
                getStatusIcon={getStatusIcon}
                getStatusColor={getStatusColor}
                onTrackOrder={handleTrackOrder}
                onReorder={handleReorder}
                onCallPharmacy={handleCallPharmacy}
              />
            ))}
          </TabsContent>

          <TabsContent value="shipped" className="space-y-4">
            {getOrdersByStatus("shipped").map((order) => (
              <OrderCard
                key={order.id}
                order={order}
                getStatusIcon={getStatusIcon}
                getStatusColor={getStatusColor}
                onTrackOrder={handleTrackOrder}
                onReorder={handleReorder}
                onCallPharmacy={handleCallPharmacy}
              />
            ))}
          </TabsContent>

          <TabsContent value="delivered" className="space-y-4">
            {getOrdersByStatus("delivered").map((order) => (
              <OrderCard
                key={order.id}
                order={order}
                getStatusIcon={getStatusIcon}
                getStatusColor={getStatusColor}
                onTrackOrder={handleTrackOrder}
                onReorder={handleReorder}
                onCallPharmacy={handleCallPharmacy}
              />
            ))}
          </TabsContent>

          <TabsContent value="cancelled" className="space-y-4">
            {getOrdersByStatus("cancelled").map((order) => (
              <OrderCard
                key={order.id}
                order={order}
                getStatusIcon={getStatusIcon}
                getStatusColor={getStatusColor}
                onTrackOrder={handleTrackOrder}
                onReorder={handleReorder}
                onCallPharmacy={handleCallPharmacy}
              />
            ))}
          </TabsContent>
        </Tabs>

        {filteredOrders.length === 0 && (
          <div className="text-center py-12">
            <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-600 mb-2">No orders found</h3>
            <p className="text-gray-500 mb-6">
              {searchQuery ? "Try adjusting your search terms" : "You haven't placed any orders yet"}
            </p>
            <Button asChild>
              <Link href="/search">Start Shopping</Link>
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}

function OrderCard({
  order,
  getStatusIcon,
  getStatusColor,
  onTrackOrder,
  onReorder,
  onCallPharmacy,
}: {
  order: Order
  getStatusIcon: (status: string) => React.ReactNode
  getStatusColor: (status: string) => string
  onTrackOrder: (trackingNumber: string) => void
  onReorder: (orderId: string) => void
  onCallPharmacy: (phone: string) => void
}) {
  return (
    <Card className="overflow-hidden">
      <CardHeader className="bg-gray-50">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">{order.orderNumber}</CardTitle>
            <p className="text-sm text-gray-600">Ordered on {new Date(order.date).toLocaleDateString()}</p>
          </div>
          <div className="text-right">
            <Badge className={`${getStatusColor(order.status)} mb-2`}>
              <span className="flex items-center space-x-1">
                {getStatusIcon(order.status)}
                <span className="capitalize">{order.status}</span>
              </span>
            </Badge>
            <p className="text-xl font-bold">{order.total.toLocaleString()} FCFA</p>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-6">
        <div className="grid md:grid-cols-3 gap-6">
          <div>
            <h4 className="font-semibold mb-2">Items</h4>
            <div className="space-y-1">
              {order.items.map((item, index) => (
                <div key={index} className="text-sm">
                  <span className="font-medium">{item.name}</span>
                  <span className="text-gray-600"> × {item.quantity}</span>
                  <span className="float-right">{item.price.toLocaleString()} FCFA</span>
                </div>
              ))}
            </div>
          </div>

          <div>
            <h4 className="font-semibold mb-2">Pharmacy</h4>
            <div className="text-sm">
              <p className="font-medium">{order.pharmacy.name}</p>
              <p className="text-gray-600">{order.pharmacy.address}</p>
              <p className="text-gray-600">{order.pharmacy.phone}</p>
              <p className="mt-2">
                <Badge variant="outline" className="capitalize">
                  {order.deliveryMethod}
                </Badge>
              </p>
            </div>
          </div>

          <div>
            <h4 className="font-semibold mb-2">Delivery Info</h4>
            <div className="text-sm space-y-1">
              {order.estimatedDelivery && (
                <p>
                  <span className="text-gray-600">Est. delivery: </span>
                  {new Date(order.estimatedDelivery).toLocaleDateString()}
                </p>
              )}
              {order.trackingNumber && (
                <p>
                  <span className="text-gray-600">Tracking: </span>
                  <span className="font-mono">{order.trackingNumber}</span>
                </p>
              )}
            </div>
          </div>
        </div>

        <div className="flex justify-between items-center mt-6 pt-4 border-t">
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" asChild>
              <Link href={`/orders/${order.id}`}>
                <Eye className="h-4 w-4 mr-1" />
                View Details
              </Link>
            </Button>
            {order.status === "delivered" && (
              <Button variant="outline" size="sm" onClick={() => onReorder(order.id)}>
                <RotateCcw className="h-4 w-4 mr-1" />
                Reorder
              </Button>
            )}
            <Button variant="outline" size="sm" onClick={() => onCallPharmacy(order.pharmacy.phone)}>
              <Phone className="h-4 w-4 mr-1" />
              Call Pharmacy
            </Button>
          </div>

          {order.trackingNumber && order.status === "shipped" && (
            <Button size="sm" onClick={() => onTrackOrder(order.trackingNumber!)}>
              <Truck className="h-4 w-4 mr-1" />
              Track Package
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
