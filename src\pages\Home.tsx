import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { UserMenu } from "@/components/user-menu"
import { Search, MapPin, Clock, Shield, ShoppingCart } from "lucide-react"
import { Link, useNavigate } from "react-router-dom"
import { useState, type FormEvent } from "react"

export default function Home() {
  const [searchQuery, setSearchQuery] = useState("")
  const navigate = useNavigate()

  const handleSearch = (e: FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery)}`)
    }
  }

  const popularMedications = ["Paracetamol", "Chloroquine", "Amoxicillin", "Artemether", "Coartem", "Flagyl"]

  const features = [
    {
      icon: Search,
      title: "Smart Search",
      description: "Find medications across multiple pharmacies instantly",
    },
    {
      icon: MapPin,
      title: "Location-Based",
      description: "Discover nearby pharmacies with real-time availability",
    },
    {
      icon: Clock,
      title: "Quick Delivery",
      description: "Same-day delivery or convenient pickup options",
    },
    {
      icon: Shield,
      title: "Secure & Safe",
      description: "Licensed pharmacies with verified medications",
    },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link to="/" className="text-2xl font-bold text-blue-600">
            PharmaCare
          </Link>
          <nav className="hidden md:flex items-center space-x-6">
            <Link to="/search" className="text-gray-600 hover:text-blue-600">
              Search
            </Link>
            <Link to="/regions" className="text-gray-600 hover:text-blue-600">
              Regions
            </Link>
            <Link to="/map" className="text-gray-600 hover:text-blue-600">
              Map
            </Link>
            <Link to="/orders" className="text-gray-600 hover:text-blue-600">
              Orders
            </Link>
          </nav>
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="icon">
              <ShoppingCart className="h-5 w-5" />
            </Button>
            <UserMenu />
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="container mx-auto px-4 py-16 text-center">
        <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
          Find Your Medications
          <span className="text-blue-600 block">Across Cameroon</span>
        </h1>
        <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
          Search across pharmacies in Douala, Yaoundé, Bamenda, and other cities. Compare prices and get your
          medications delivered nationwide.
        </p>

        {/* Search Bar */}
        <form onSubmit={handleSearch} className="max-w-2xl mx-auto mb-8">
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              type="text"
              placeholder="Search for medications, pharmacies, or conditions..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-12 pr-4 py-4 text-lg rounded-full border-2 border-gray-200 focus:border-blue-500"
            />
            <Button
              type="submit"
              size="lg"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 rounded-full"
            >
              Search
            </Button>
          </div>
        </form>

        {/* Popular Medications */}
        <div className="flex flex-wrap justify-center gap-2 mb-8">
          <span className="text-sm text-gray-600 mr-2">Popular:</span>
          {popularMedications.map((med) => (
            <Badge
              key={med}
              variant="secondary"
              className="cursor-pointer hover:bg-blue-100"
              onClick={() => {
                setSearchQuery(med)
                navigate(`/search?q=${encodeURIComponent(med)}`)
              }}
            >
              {med}
            </Badge>
          ))}
        </div>
      </section>

      {/* Features Section */}
      <section className="container mx-auto px-4 py-16">
        <h2 className="text-3xl font-bold text-center mb-12">Why Choose PharmaCare?</h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <Card key={index} className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <feature.icon className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <CardTitle className="text-xl">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Regions Section */}
      <section className="container mx-auto px-4 py-16">
        <h2 className="text-3xl font-bold text-center mb-12">Coverage Across Cameroon</h2>
        <div className="grid md:grid-cols-3 gap-8 mb-8">
          <Card className="text-center hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="text-blue-600">Littoral & Centre</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">
                Major cities including Douala and Yaoundé with extensive pharmacy networks
              </p>
              <Button className="mt-4 bg-transparent" variant="outline" asChild>
                <Link to="/search?region=littoral,centre">Explore</Link>
              </Button>
            </CardContent>
          </Card>
          <Card className="text-center hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="text-blue-600">North & Far North</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">
                Serving Garoua, Maroua, and surrounding communities with reliable access
              </p>
              <Button className="mt-4" variant="outline" asChild>
                <Link to="/search?region=north,far-north">Explore</Link>
              </Button>
            </CardContent>
          </Card>
          <Card className="text-center hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="text-blue-600">West & Northwest</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">
                Comprehensive coverage in Bamenda, Bafoussam, and the highlands region
              </p>
              <Button className="mt-4" variant="outline" asChild>
                <Link to="/search?region=west,northwest">Explore</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-4 py-16 text-center">
        <h2 className="text-3xl font-bold mb-6">Ready to Get Started?</h2>
        <p className="text-xl text-gray-600 mb-8">
          Join thousands of customers who trust PharmaCare for their medication needs.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button size="lg" asChild>
            <Link to="/search">
              <Search className="mr-2 h-5 w-5" />
              Start Searching
            </Link>
          </Button>
          <Button size="lg" variant="outline" asChild>
            <Link to="/map">
              <MapPin className="mr-2 h-5 w-5" />
              Find Pharmacies
            </Link>
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">PharmaCare</h3>
              <p className="text-gray-400">
                Your trusted partner for finding medications across Cameroon.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link to="/search" className="hover:text-white">
                    Search Medications
                  </Link>
                </li>
                <li>
                  <Link to="/regions" className="hover:text-white">
                    Browse Regions
                  </Link>
                </li>
                <li>
                  <Link to="/map" className="hover:text-white">
                    Find Pharmacies
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <a href="#" className="hover:text-white">
                    Help Center
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white">
                    Contact Us
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white">
                    Privacy Policy
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Connect</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <a href="#" className="hover:text-white">
                    Facebook
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white">
                    Twitter
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white">
                    WhatsApp
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 PharmaCare. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
