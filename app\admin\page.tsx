"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import {
  Users,
  Building2,
  Package,
  TrendingUp,
  Search,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  Eye,
  Edit,
  Trash2,
  Plus,
  AlertTriangle,
  <PERSON>tings,
  Shield,
  Clock,
  Star,
} from "lucide-react"
import Link from "next/link"
import { useState } from "react"

export default function AdminDashboard() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedTab, setSelectedTab] = useState("overview")
  const [isAddPharmacyOpen, setIsAddPharmacyOpen] = useState(false)
  const [isAddMedicationOpen, setIsAddMedicationOpen] = useState(false)

  // Mock data for admin dashboard
  const stats = {
    totalUsers: 8420,
    totalPharmacies: 156,
    totalOrders: 4765,
    monthlyRevenue: 45000000, // in FCFA
    pendingOrders: 23,
    activeDeliveries: 45,
    lowStockAlerts: 12,
    systemUptime: "99.8%",
  }

  const pendingPharmacies = [
    {
      id: "1",
      name: "Pharmacie de la Paix",
      owner: "Dr. Marie Ngozi",
      email: "<EMAIL>",
      phone: "+237 233 45 67 89",
      address: "Quartier Bonanjo, Douala, Littoral",
      submittedDate: "2024-01-20",
      status: "pending",
      documents: ["License", "Tax Certificate", "Owner ID"],
      region: "Littoral",
    },
    {
      id: "2",
      name: "Pharmacie du Progrès",
      owner: "Dr. Paul Mbarga",
      email: "<EMAIL>",
      phone: "+237 222 34 56 78",
      address: "Quartier Bastos, Yaoundé, Centre",
      submittedDate: "2024-01-18",
      status: "pending",
      documents: ["License", "Tax Certificate"],
      region: "Centre",
    },
  ]

  const allPharmacies = [
    {
      id: "p1",
      name: "Pharmacie du Centre",
      owner: "Dr. Jean Kamga",
      email: "<EMAIL>",
      phone: "+237 233 42 15 67",
      address: "Avenue Kennedy, Douala",
      region: "Littoral",
      status: "active",
      rating: 4.8,
      totalOrders: 1250,
      revenue: 15000000,
      joinDate: "2023-06-15",
      lastActive: "2024-01-28",
    },
    {
      id: "p2",
      name: "Pharmacie Centrale",
      owner: "Dr. Sarah Biya",
      email: "<EMAIL>",
      phone: "+237 222 23 45 78",
      address: "Rue de la Réunification, Yaoundé",
      region: "Centre",
      status: "active",
      rating: 4.6,
      totalOrders: 980,
      revenue: 12000000,
      joinDate: "2023-08-20",
      lastActive: "2024-01-28",
    },
  ]

  const allUsers = [
    {
      id: "1",
      name: "John Doe",
      email: "<EMAIL>",
      phone: "+237 677 12 34 56",
      region: "Littoral",
      city: "Douala",
      joinDate: "2024-01-25",
      orders: 3,
      totalSpent: 45000,
      status: "active",
      lastLogin: "2024-01-28",
    },
    {
      id: "2",
      name: "Jane Smith",
      email: "<EMAIL>",
      phone: "+237 655 98 76 54",
      region: "Centre",
      city: "Yaoundé",
      joinDate: "2024-01-24",
      orders: 1,
      totalSpent: 12000,
      status: "active",
      lastLogin: "2024-01-27",
    },
  ]

  const medications = [
    {
      id: "m1",
      name: "Paracetamol 500mg",
      category: "Pain Relief",
      manufacturer: "GSK",
      avgPrice: 2500,
      totalStock: 15000,
      lowStockThreshold: 1000,
      pharmaciesStocking: 45,
      totalSold: 8500,
      status: "active",
    },
    {
      id: "m2",
      name: "Coartem",
      category: "Antimalarial",
      manufacturer: "Novartis",
      avgPrice: 8500,
      totalStock: 500,
      lowStockThreshold: 100,
      pharmaciesStocking: 32,
      totalSold: 2100,
      status: "active",
    },
  ]

  const recentOrders = [
    {
      id: "ORD-001",
      customer: "John Doe",
      pharmacy: "Pharmacie du Centre",
      items: 2,
      total: 18500,
      status: "delivered",
      date: "2024-01-28",
      region: "Littoral",
    },
    {
      id: "ORD-002",
      customer: "Jane Smith",
      pharmacy: "Pharmacie Centrale",
      items: 1,
      total: 8500,
      status: "pending",
      date: "2024-01-28",
      region: "Centre",
    },
  ]

  const systemLogs = [
    {
      id: "1",
      timestamp: "2024-01-28 14:30:25",
      level: "INFO",
      action: "User Login",
      user: "<EMAIL>",
      details: "Successful login from Douala",
    },
    {
      id: "2",
      timestamp: "2024-01-28 14:25:10",
      level: "WARNING",
      action: "Low Stock Alert",
      user: "system",
      details: "Coartem stock below threshold at Pharmacie Moderne",
    },
  ]

  const handleApprovePharmacy = (id: string) => {
    console.log(`Approving pharmacy ${id}`)
  }

  const handleRejectPharmacy = (id: string) => {
    console.log(`Rejecting pharmacy ${id}`)
  }

  const handleSuspendUser = (id: string) => {
    console.log(`Suspending user ${id}`)
  }

  const handleDeleteMedication = (id: string) => {
    console.log(`Deleting medication ${id}`)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="border-b bg-white sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="text-2xl font-bold text-blue-600">
            PharmaCare Admin
          </Link>
          <div className="flex items-center space-x-4">
            <Badge variant="outline" className="text-green-600">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
              System Online
            </Badge>
            <Button variant="outline">
              <Link href="/">View Site</Link>
            </Button>
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <Button variant="outline">Logout</Button>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">System Administration</h1>
          <p className="text-gray-600">
            Manage users, pharmacies, medications, and monitor platform analytics across Cameroon
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalUsers.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">+12% from last month</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Active Pharmacies</CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalPharmacies}</div>
              <p className="text-xs text-muted-foreground">+8% from last month</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalOrders.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">+23% from last month</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{(stats.monthlyRevenue / 1000000).toFixed(1)}M FCFA</div>
              <p className="text-xs text-muted-foreground">+18% from last month</p>
            </CardContent>
          </Card>
        </div>

        {/* Alert Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <Card className="border-yellow-200 bg-yellow-50">
            <CardContent className="pt-4">
              <div className="flex items-center">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2" />
                <div>
                  <div className="text-lg font-bold text-yellow-800">{stats.pendingOrders}</div>
                  <div className="text-sm text-yellow-600">Pending Orders</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="pt-4">
              <div className="flex items-center">
                <Clock className="h-5 w-5 text-blue-600 mr-2" />
                <div>
                  <div className="text-lg font-bold text-blue-800">{stats.activeDeliveries}</div>
                  <div className="text-sm text-blue-600">Active Deliveries</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-4">
              <div className="flex items-center">
                <Package className="h-5 w-5 text-red-600 mr-2" />
                <div>
                  <div className="text-lg font-bold text-red-800">{stats.lowStockAlerts}</div>
                  <div className="text-sm text-red-600">Low Stock Alerts</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-green-200 bg-green-50">
            <CardContent className="pt-4">
              <div className="flex items-center">
                <Shield className="h-5 w-5 text-green-600 mr-2" />
                <div>
                  <div className="text-lg font-bold text-green-800">{stats.systemUptime}</div>
                  <div className="text-sm text-green-600">System Uptime</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-7">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="pharmacies">Pharmacies</TabsTrigger>
            <TabsTrigger value="users">Users</TabsTrigger>
            <TabsTrigger value="medications">Medications</TabsTrigger>
            <TabsTrigger value="orders">Orders</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="system">System</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Orders</CardTitle>
                  <CardDescription>Latest orders across all pharmacies</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {recentOrders.map((order) => (
                      <div key={order.id} className="flex items-center justify-between border-b pb-2">
                        <div>
                          <p className="font-medium">{order.id}</p>
                          <p className="text-sm text-gray-600">
                            {order.customer} • {order.pharmacy}
                          </p>
                          <p className="text-xs text-gray-500">{order.region}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold">{order.total.toLocaleString()} FCFA</p>
                          <Badge
                            className={
                              order.status === "delivered"
                                ? "bg-green-100 text-green-800"
                                : "bg-yellow-100 text-yellow-800"
                            }
                          >
                            {order.status}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>System Activity</CardTitle>
                  <CardDescription>Recent system logs and activities</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {systemLogs.map((log) => (
                      <div key={log.id} className="text-sm">
                        <div className="flex items-center justify-between">
                          <span className="font-medium">{log.action}</span>
                          <Badge variant={log.level === "WARNING" ? "destructive" : "secondary"}>{log.level}</Badge>
                        </div>
                        <p className="text-gray-600">{log.details}</p>
                        <p className="text-xs text-gray-500">{log.timestamp}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="pharmacies" className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold">Pharmacy Management</h2>
              <Dialog open={isAddPharmacyOpen} onOpenChange={setIsAddPharmacyOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Pharmacy
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>Add New Pharmacy</DialogTitle>
                    <DialogDescription>Register a new pharmacy in the system</DialogDescription>
                  </DialogHeader>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="pharmacyName">Pharmacy Name</Label>
                      <Input id="pharmacyName" placeholder="Pharmacie Example" />
                    </div>
                    <div>
                      <Label htmlFor="ownerName">Owner Name</Label>
                      <Input id="ownerName" placeholder="Dr. John Doe" />
                    </div>
                    <div>
                      <Label htmlFor="email">Email</Label>
                      <Input id="email" type="email" placeholder="<EMAIL>" />
                    </div>
                    <div>
                      <Label htmlFor="phone">Phone</Label>
                      <Input id="phone" placeholder="+237 6XX XX XX XX" />
                    </div>
                    <div className="col-span-2">
                      <Label htmlFor="address">Address</Label>
                      <Input id="address" placeholder="Street, City, Region" />
                    </div>
                    <div>
                      <Label htmlFor="region">Region</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select region" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="centre">Centre</SelectItem>
                          <SelectItem value="littoral">Littoral</SelectItem>
                          <SelectItem value="ouest">Ouest</SelectItem>
                          <SelectItem value="nord-ouest">Nord-Ouest</SelectItem>
                          <SelectItem value="sud-ouest">Sud-Ouest</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="status">Status</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="pending">Pending</SelectItem>
                          <SelectItem value="suspended">Suspended</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsAddPharmacyOpen(false)}>
                      Cancel
                    </Button>
                    <Button>Add Pharmacy</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>

            {/* Pending Applications */}
            <Card>
              <CardHeader>
                <CardTitle>Pending Applications ({pendingPharmacies.length})</CardTitle>
                <CardDescription>Review and approve new pharmacy registrations</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {pendingPharmacies.map((pharmacy) => (
                    <div key={pharmacy.id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="space-y-2 flex-1">
                          <h4 className="font-semibold text-lg">{pharmacy.name}</h4>
                          <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                            <div>
                              <p>
                                <strong>Owner:</strong> {pharmacy.owner}
                              </p>
                              <p>
                                <strong>Email:</strong> {pharmacy.email}
                              </p>
                              <p>
                                <strong>Phone:</strong> {pharmacy.phone}
                              </p>
                            </div>
                            <div>
                              <p>
                                <strong>Region:</strong> {pharmacy.region}
                              </p>
                              <p>
                                <strong>Submitted:</strong> {new Date(pharmacy.submittedDate).toLocaleDateString()}
                              </p>
                              <p>
                                <strong>Documents:</strong> {pharmacy.documents.join(", ")}
                              </p>
                            </div>
                          </div>
                          <p className="text-sm">
                            <strong>Address:</strong> {pharmacy.address}
                          </p>
                        </div>
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            onClick={() => handleApprovePharmacy(pharmacy.id)}
                            className="bg-green-600 hover:bg-green-700"
                          >
                            <CheckCircle className="h-4 w-4 mr-1" />
                            Approve
                          </Button>
                          <Button size="sm" variant="destructive" onClick={() => handleRejectPharmacy(pharmacy.id)}>
                            <XCircle className="h-4 w-4 mr-1" />
                            Reject
                          </Button>
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4 mr-1" />
                            Details
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* All Pharmacies */}
            <Card>
              <CardHeader>
                <CardTitle>All Pharmacies ({allPharmacies.length})</CardTitle>
                <CardDescription>Manage all registered pharmacies</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Owner</TableHead>
                      <TableHead>Region</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Rating</TableHead>
                      <TableHead>Orders</TableHead>
                      <TableHead>Revenue</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {allPharmacies.map((pharmacy) => (
                      <TableRow key={pharmacy.id}>
                        <TableCell className="font-medium">{pharmacy.name}</TableCell>
                        <TableCell>{pharmacy.owner}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{pharmacy.region}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge
                            className={
                              pharmacy.status === "active" ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
                            }
                          >
                            {pharmacy.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400 mr-1" />
                            {pharmacy.rating}
                          </div>
                        </TableCell>
                        <TableCell>{pharmacy.totalOrders}</TableCell>
                        <TableCell>{(pharmacy.revenue / 1000000).toFixed(1)}M FCFA</TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button size="sm" variant="outline">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="users" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>User Management</CardTitle>
                    <CardDescription>Manage registered users and their accounts</CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      <Input
                        placeholder="Search users..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10 w-64"
                      />
                    </div>
                    <Select defaultValue="all">
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Users</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Phone</TableHead>
                      <TableHead>Location</TableHead>
                      <TableHead>Orders</TableHead>
                      <TableHead>Total Spent</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {allUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell className="font-medium">{user.name}</TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>{user.phone}</TableCell>
                        <TableCell>
                          {user.city}, {user.region}
                        </TableCell>
                        <TableCell>{user.orders}</TableCell>
                        <TableCell>{user.totalSpent.toLocaleString()} FCFA</TableCell>
                        <TableCell>
                          <Badge
                            className={
                              user.status === "active" ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
                            }
                          >
                            {user.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button size="sm" variant="outline">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => handleSuspendUser(user.id)}>
                              <AlertTriangle className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="medications" className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold">Medication Management</h2>
              <Dialog open={isAddMedicationOpen} onOpenChange={setIsAddMedicationOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Medication
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add New Medication</DialogTitle>
                    <DialogDescription>Add a new medication to the system</DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="medName">Medication Name</Label>
                      <Input id="medName" placeholder="Paracetamol 500mg" />
                    </div>
                    <div>
                      <Label htmlFor="category">Category</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="pain-relief">Pain Relief</SelectItem>
                          <SelectItem value="antimalarial">Antimalarial</SelectItem>
                          <SelectItem value="antibiotic">Antibiotic</SelectItem>
                          <SelectItem value="vitamins">Vitamins</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="manufacturer">Manufacturer</Label>
                      <Input id="manufacturer" placeholder="GSK, Novartis, etc." />
                    </div>
                    <div>
                      <Label htmlFor="avgPrice">Average Price (FCFA)</Label>
                      <Input id="avgPrice" type="number" placeholder="2500" />
                    </div>
                    <div>
                      <Label htmlFor="description">Description</Label>
                      <Textarea id="description" placeholder="Medication description..." />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsAddMedicationOpen(false)}>
                      Cancel
                    </Button>
                    <Button>Add Medication</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>All Medications ({medications.length})</CardTitle>
                <CardDescription>Manage medication catalog and inventory</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Manufacturer</TableHead>
                      <TableHead>Avg Price</TableHead>
                      <TableHead>Total Stock</TableHead>
                      <TableHead>Pharmacies</TableHead>
                      <TableHead>Total Sold</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {medications.map((med) => (
                      <TableRow key={med.id}>
                        <TableCell className="font-medium">{med.name}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{med.category}</Badge>
                        </TableCell>
                        <TableCell>{med.manufacturer}</TableCell>
                        <TableCell>{med.avgPrice.toLocaleString()} FCFA</TableCell>
                        <TableCell>
                          <div className={med.totalStock < med.lowStockThreshold ? "text-red-600 font-semibold" : ""}>
                            {med.totalStock.toLocaleString()}
                            {med.totalStock < med.lowStockThreshold && (
                              <AlertTriangle className="h-4 w-4 inline ml-1" />
                            )}
                          </div>
                        </TableCell>
                        <TableCell>{med.pharmaciesStocking}</TableCell>
                        <TableCell>{med.totalSold.toLocaleString()}</TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button size="sm" variant="outline">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => handleDeleteMedication(med.id)}>
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="orders" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Order Management</CardTitle>
                <CardDescription>Monitor and manage all orders across the platform</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Order ID</TableHead>
                      <TableHead>Customer</TableHead>
                      <TableHead>Pharmacy</TableHead>
                      <TableHead>Items</TableHead>
                      <TableHead>Total</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {recentOrders.map((order) => (
                      <TableRow key={order.id}>
                        <TableCell className="font-medium">{order.id}</TableCell>
                        <TableCell>{order.customer}</TableCell>
                        <TableCell>{order.pharmacy}</TableCell>
                        <TableCell>{order.items}</TableCell>
                        <TableCell>{order.total.toLocaleString()} FCFA</TableCell>
                        <TableCell>
                          <Badge
                            className={
                              order.status === "delivered"
                                ? "bg-green-100 text-green-800"
                                : "bg-yellow-100 text-yellow-800"
                            }
                          >
                            {order.status}
                          </Badge>
                        </TableCell>
                        <TableCell>{order.date}</TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button size="sm" variant="outline">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline">
                              <Edit className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Revenue Analytics</CardTitle>
                  <CardDescription>Monthly revenue trends across regions</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Littoral (Douala)</span>
                      <span className="font-semibold">18.5M FCFA</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Centre (Yaoundé)</span>
                      <span className="font-semibold">15.2M FCFA</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Ouest (Bafoussam)</span>
                      <span className="font-semibold">6.8M FCFA</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Nord-Ouest (Bamenda)</span>
                      <span className="font-semibold">4.5M FCFA</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Top Performing Pharmacies</CardTitle>
                  <CardDescription>Highest revenue generating pharmacies</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {allPharmacies.map((pharmacy, index) => (
                      <div key={pharmacy.id} className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">{pharmacy.name}</p>
                          <p className="text-sm text-gray-600">{pharmacy.region}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold">{(pharmacy.revenue / 1000000).toFixed(1)}M FCFA</p>
                          <div className="flex items-center">
                            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400 mr-1" />
                            <span className="text-sm">{pharmacy.rating}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="system" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>System Settings</CardTitle>
                  <CardDescription>Configure system-wide settings</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="maintenance">Maintenance Mode</Label>
                      <p className="text-sm text-gray-600">Enable maintenance mode for system updates</p>
                    </div>
                    <Switch id="maintenance" />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="notifications">Email Notifications</Label>
                      <p className="text-sm text-gray-600">Send email notifications to users</p>
                    </div>
                    <Switch id="notifications" defaultChecked />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="autoApprove">Auto-approve Pharmacies</Label>
                      <p className="text-sm text-gray-600">Automatically approve verified pharmacies</p>
                    </div>
                    <Switch id="autoApprove" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>System Logs</CardTitle>
                  <CardDescription>Recent system activities and errors</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 max-h-64 overflow-y-auto">
                    {systemLogs.map((log) => (
                      <div key={log.id} className="text-sm border-b pb-2">
                        <div className="flex items-center justify-between">
                          <span className="font-medium">{log.action}</span>
                          <Badge variant={log.level === "WARNING" ? "destructive" : "secondary"}>{log.level}</Badge>
                        </div>
                        <p className="text-gray-600">{log.details}</p>
                        <p className="text-xs text-gray-500">{log.timestamp}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
