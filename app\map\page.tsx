"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { GoogleMap } from "@/components/google-map"
import { UserMenu } from "@/components/user-menu"
import { Search, MapPin, Star, Phone, Navigation, ShoppingCart, Loader2, AlertCircle, Filter } from "lucide-react"
import Link from "next/link"
import { useState, useEffect } from "react"
import { getCurrentLocation, calculateDistance, type MapLocation, type PharmacyLocation } from "@/lib/google-maps"

export default function MapPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedPharmacy, setSelectedPharmacy] = useState<PharmacyLocation | null>(null)
  const [userLocation, setUserLocation] = useState<MapLocation | null>(null)
  const [locationError, setLocationError] = useState<string | null>(null)
  const [loadingLocation, setLoadingLocation] = useState(false)
  const [pharmacies, setPharmacies] = useState<PharmacyLocation[]>([])
  const [showFilters, setShowFilters] = useState(false)

  // Real pharmacy locations across Cameroon
  const cameroonPharmacies: PharmacyLocation[] = [
    // Douala - Littoral
    {
      id: "p1",
      name: "Pharmacie du Centre",
      address: "Avenue Kennedy, Akwa, Douala",
      phone: "+237 233 42 15 67",
      rating: 4.8,
      lat: 4.0511,
      lng: 9.7679,
      isOpen: true,
    },
    {
      id: "p2",
      name: "Pharmacie Bonanjo",
      address: "Rue Joss, Bonanjo, Douala",
      phone: "+237 233 42 28 91",
      rating: 4.6,
      lat: 4.0469,
      lng: 9.7069,
      isOpen: true,
    },
    {
      id: "p3",
      name: "Pharmacie Deido",
      address: "Marché Deido, Deido, Douala",
      phone: "+237 233 39 45 12",
      rating: 4.4,
      lat: 4.0733,
      lng: 9.7347,
      isOpen: false,
    },
    // Yaoundé - Centre
    {
      id: "p4",
      name: "Pharmacie Centrale",
      address: "Avenue de l'Indépendance, Yaoundé",
      phone: "+237 222 23 45 78",
      rating: 4.7,
      lat: 3.848,
      lng: 11.5021,
      isOpen: true,
    },
    {
      id: "p5",
      name: "Pharmacie de l'Espoir",
      address: "Quartier Mvog-Ada, Yaoundé",
      phone: "+237 222 15 78 90",
      rating: 4.5,
      lat: 3.8667,
      lng: 11.5167,
      isOpen: true,
    },
    {
      id: "p6",
      name: "Pharmacie Bastos",
      address: "Quartier Bastos, Yaoundé",
      phone: "+237 222 20 34 56",
      rating: 4.9,
      lat: 3.8792,
      lng: 11.5208,
      isOpen: true,
    },
    // Bamenda - Nord-Ouest
    {
      id: "p7",
      name: "Pharmacie Moderne",
      address: "Commercial Avenue, Bamenda",
      phone: "+237 233 36 24 89",
      rating: 4.3,
      lat: 5.9631,
      lng: 10.1591,
      isOpen: true,
    },
    {
      id: "p8",
      name: "Pharmacie City Chemist",
      address: "Ntarikon Park, Bamenda",
      phone: "+237 233 36 45 23",
      rating: 4.6,
      lat: 5.9597,
      lng: 10.145,
      isOpen: false,
    },
    // Bafoussam - Ouest
    {
      id: "p9",
      name: "Pharmacie Saint-Michel",
      address: "Rue des Palmiers, Bafoussam",
      phone: "+237 233 44 12 34",
      rating: 4.4,
      lat: 5.4737,
      lng: 10.4176,
      isOpen: true,
    },
    {
      id: "p10",
      name: "Pharmacie du Progrès",
      address: "Marché A, Bafoussam",
      phone: "+237 233 44 67 89",
      rating: 4.2,
      lat: 5.4781,
      lng: 10.4167,
      isOpen: true,
    },
    // Additional pharmacies across other regions
    {
      id: "p11",
      name: "Pharmacie Mount Cameroon",
      address: "Molyko, Buea",
      phone: "+237 233 32 45 67",
      rating: 4.5,
      lat: 4.156,
      lng: 9.2415,
      isOpen: true,
    },
    {
      id: "p12",
      name: "Pharmacie du Nord",
      address: "Centre Commercial, Garoua",
      phone: "+237 222 27 34 56",
      rating: 4.1,
      lat: 9.3265,
      lng: 13.3958,
      isOpen: false,
    },
  ]

  // Get user's current location
  const handleGetCurrentLocation = async () => {
    setLoadingLocation(true)
    setLocationError(null)

    try {
      const location = await getCurrentLocation()
      setUserLocation(location)

      // Calculate distances and sort pharmacies
      const pharmaciesWithDistance = cameroonPharmacies.map((pharmacy) => ({
        ...pharmacy,
        distance: calculateDistance(location, { lat: pharmacy.lat, lng: pharmacy.lng }),
      }))

      // Sort by distance
      pharmaciesWithDistance.sort((a, b) => a.distance - b.distance)
      setPharmacies(pharmaciesWithDistance as PharmacyLocation[])
    } catch (error) {
      console.error("Error getting location:", error)
      setLocationError("Unable to get your location. Please enable location services.")
      setPharmacies(cameroonPharmacies)
    } finally {
      setLoadingLocation(false)
    }
  }

  // Initialize pharmacies on component mount
  useEffect(() => {
    setPharmacies(cameroonPharmacies)
  }, [])

  const filteredPharmacies = pharmacies.filter(
    (pharmacy) =>
      pharmacy.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      pharmacy.address.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const handlePharmacySelect = (pharmacy: PharmacyLocation) => {
    setSelectedPharmacy(pharmacy)
  }

  const handleCallPharmacy = (phone: string) => {
    window.open(`tel:${phone}`, "_self")
  }

  const handleGetDirections = (pharmacy: PharmacyLocation) => {
    const destination = `${pharmacy.lat},${pharmacy.lng}`
    const url = `https://www.google.com/maps/dir/?api=1&destination=${destination}&destination_place_id=${pharmacy.name}`
    window.open(url, "_blank")
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="border-b bg-white sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="text-2xl font-bold text-blue-600">
            PharmaCare
          </Link>
          <div className="flex-1 max-w-2xl mx-8">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                type="text"
                placeholder="Search pharmacies by name or location..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 pr-4 py-2"
              />
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="icon">
              <ShoppingCart className="h-5 w-5" />
            </Button>
            <UserMenu />
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-6">
        {/* Location Controls */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-2xl font-bold">Find Pharmacies Near You</h1>
            <div className="flex items-center space-x-2">
              <Button variant="outline" onClick={() => setShowFilters(!showFilters)}>
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
              <Button onClick={handleGetCurrentLocation} disabled={loadingLocation} className="flex items-center">
                {loadingLocation ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Navigation className="h-4 w-4 mr-2" />
                )}
                {loadingLocation ? "Getting Location..." : "Use My Location"}
              </Button>
            </div>
          </div>

          {locationError && (
            <Alert className="mb-4" variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{locationError}</AlertDescription>
            </Alert>
          )}

          {userLocation && (
            <Alert className="mb-4">
              <MapPin className="h-4 w-4" />
              <AlertDescription>
                Location found! Showing {filteredPharmacies.length} pharmacies sorted by distance.
              </AlertDescription>
            </Alert>
          )}
        </div>

        <div className="flex flex-col lg:flex-row gap-6 h-[calc(100vh-250px)]">
          {/* Pharmacy List */}
          <div className="lg:w-96 space-y-4 overflow-y-auto">
            <div className="flex items-center justify-between">
              <Badge variant="secondary">{filteredPharmacies.length} pharmacies found</Badge>
              {userLocation && <Badge variant="outline">Sorted by distance</Badge>}
            </div>

            {filteredPharmacies.map((pharmacy) => (
              <Card
                key={pharmacy.id}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  selectedPharmacy?.id === pharmacy.id ? "ring-2 ring-blue-500" : ""
                }`}
                onClick={() => handlePharmacySelect(pharmacy)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg flex items-center">
                        {pharmacy.name}
                        <Badge
                          className={`ml-2 text-xs ${
                            pharmacy.isOpen ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                          }`}
                        >
                          {pharmacy.isOpen ? "Open" : "Closed"}
                        </Badge>
                      </CardTitle>
                      <div className="flex items-center space-x-2 text-sm text-gray-600 mt-1">
                        <div className="flex items-center">
                          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                          <span className="ml-1">{pharmacy.rating}</span>
                        </div>
                        {(pharmacy as any).distance && (
                          <>
                            <span>•</span>
                            <div className="flex items-center text-blue-600 font-semibold">
                              <MapPin className="h-4 w-4" />
                              <span className="ml-1">{(pharmacy as any).distance.toFixed(1)} km</span>
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="space-y-2">
                    <div className="flex items-center text-sm text-gray-600">
                      <MapPin className="h-4 w-4 mr-2" />
                      {pharmacy.address}
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <Phone className="h-4 w-4 mr-2" />
                      {pharmacy.phone}
                    </div>
                    <div className="flex space-x-2 mt-3">
                      <Button
                        size="sm"
                        className="flex-1"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleGetDirections(pharmacy)
                        }}
                      >
                        <Navigation className="h-4 w-4 mr-1" />
                        Directions
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="flex-1 bg-transparent"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleCallPharmacy(pharmacy.phone)
                        }}
                      >
                        <Phone className="h-4 w-4 mr-1" />
                        Call
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}

            {filteredPharmacies.length === 0 && (
              <div className="text-center py-8">
                <MapPin className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-600 mb-2">No pharmacies found</h3>
                <p className="text-gray-500">Try adjusting your search terms</p>
              </div>
            )}
          </div>

          {/* Google Map */}
          <div className="flex-1">
            <GoogleMap
              pharmacies={filteredPharmacies}
              userLocation={userLocation}
              onPharmacySelect={handlePharmacySelect}
              className="h-full w-full"
            />
          </div>
        </div>

        {/* Selected Pharmacy Details */}
        {selectedPharmacy && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>{selectedPharmacy.name}</span>
                <div className="flex items-center space-x-2">
                  <Button size="sm" onClick={() => handleCallPharmacy(selectedPharmacy.phone)}>
                    <Phone className="h-4 w-4 mr-1" />
                    Call
                  </Button>
                  <Button size="sm" variant="outline" onClick={() => handleGetDirections(selectedPharmacy)}>
                    <Navigation className="h-4 w-4 mr-1" />
                    Directions
                  </Button>
                  <Button size="sm" variant="outline" asChild>
                    <Link href={`/search?pharmacy=${selectedPharmacy.id}`}>View Medications</Link>
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-3 gap-6">
                <div>
                  <h4 className="font-semibold mb-2">Contact Information</h4>
                  <div className="space-y-1 text-sm">
                    <p>{selectedPharmacy.address}</p>
                    <p>{selectedPharmacy.phone}</p>
                    <div className="flex items-center mt-2">
                      <Badge
                        className={selectedPharmacy.isOpen ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}
                      >
                        {selectedPharmacy.isOpen ? "Currently Open" : "Currently Closed"}
                      </Badge>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Rating & Reviews</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex items-center">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400 mr-1" />
                      <span>{selectedPharmacy.rating} out of 5</span>
                    </div>
                    {(selectedPharmacy as any).distance && (
                      <div className="flex items-center text-blue-600 font-semibold">
                        <MapPin className="h-4 w-4 mr-1" />
                        <span>{(selectedPharmacy as any).distance.toFixed(1)} km away</span>
                      </div>
                    )}
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Quick Actions</h4>
                  <div className="space-y-2">
                    <Button size="sm" className="w-full" onClick={() => handleGetDirections(selectedPharmacy)}>
                      Get Directions
                    </Button>
                    <Button size="sm" variant="outline" className="w-full bg-transparent" asChild>
                      <Link href={`/search?pharmacy=${selectedPharmacy.id}`}>Browse Medications</Link>
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
