import type React from "react"

import { useEffect, useState } from "react"
import { useNavigate } from "react-router-dom"
import { getCurrentUser, isAdmin, type User } from "@/lib/auth"
import { Loader2 } from "lucide-react"

interface AuthGuardProps {
  children: React.ReactNode
  requireAdmin?: boolean
  requireAuth?: boolean
}

export function AuthGuard({ children, requireAdmin = false, requireAuth = true }: AuthGuardProps) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const navigate = useNavigate()

  useEffect(() => {
    const currentUser = getCurrentUser()
    setUser(currentUser)
    setLoading(false)

    if (requireAuth && !currentUser) {
      navigate("/auth/login")
      return
    }

    if (requireAdmin && !isAdmin(currentUser)) {
      navigate("/")
      return
    }
  }, [requireAuth, requireAdmin, navigate])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (requireAuth && !user) {
    return null
  }

  if (requireAdmin && !isAdmin(user)) {
    return null
  }

  return <>{children}</>
}
